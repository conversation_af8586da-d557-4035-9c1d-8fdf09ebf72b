- 完善需求文档
  - 补充未完成的用例详情
  - 优化各个需求的描述
  - 添加上一些前端的常见需求和用例
  - 添加基于文本游戏的常见设计需求
  - 最后重新整理需求文档
- 实现多方面的设计文档
  - 架构设计: 前后端架构, 需要说明最终设计的架构对比其他方案的优缺点
  - 数据设计: 实现存储层, 缓存层, 传输层, 界面层等不同领域的数据结构设计
  - 接口设计: 实现后端公开的接口设计
  - 前端页面原型设计: 完成各个前端用例的原型设计

- 排查需求文档中是否存在冲突的地方, 如果存在, 需要优化后面冲突的描述
- 检查需求文档中的描述中使用到大模型能力的是否都有替换为调用生成文本化接口
- 关于事件系统, 补充一点 npc 也可以创建事件, 例如玩家在xx必经之路设置了陷阱
- 补充上缺失的其他游戏系统
- 添加前置条件:
  - 用户系统采用外部 idp 系统, 游戏中的用户数据只需要保存相关的凭证和业务属性即可
  - 由于使用的 ai 能力只是调用 http 请求而已, 但是这过程中更多的是需要异步请求的能力, 所以建议使用 golang 实现后端服务
  - 由于具有数据库管理相关功能, 所以需要使用到数据库迁移功能, 每次功能调整涉及到数据结构方面的都需要创建新的数据库迁移脚本
  - http 接口的响应使用 http 状态码是否更加简洁

请按照要求更新需求文档以及所有的设计文档

- 根据现有的需求文档进行如下调整:
  - 取消文档中的标题序号排序, 将序号列表改为无序列表, 方便后续调整内容
  - 优化需求文档中的特性系统, 与记忆系统类似, 实现一个阅历系统
  - 每次经历各种事件, 或者了解了 npc, 物品等, 除了记忆外, 还可以保存为阅历, 这样在下次面对类似的事件便能更好的处理
  - 任务与成就, 声誉与关系, 技能与能力这 3 个系统应该属于记忆系统和阅历系统中, 请同步修改
  - 环境与天气系统应该属于持续性的事件, 也可以归属到事件系统, 请调整一下事件系统的说明
  - 交互示例中需要尽量减少需要实时反馈的内容, 修改为可以通过延迟获得反馈的交互
  - 经济系统是需要量化的, 有其他可以非量化的替代吗

- 根据现有的需求文档进行如下调整:
  - 多玩家同一游戏世界时尽量将玩家分开放置初始化, 但是由于整个地图是由后期按需生成的, 所以很难确定玩家间的相对位置, 能否在世界创建初期就大概知道后期的地图规模?
  - 由于文本生成的自由度问题, 所以需要一个后端的输入校验避免出现一些广告推销, 有损公德的内容
  - 调整前端核心用例, 使其描述顺序符合从进入游戏到创建世界, 再到探索世界的顺序

根据现有的需求文档, 按照你的理解, 完成这个项目的所有设计文档

- 根据现有的需求文档进行如下调整:
  - 将 api 网关, pgsql, redis, file storage 这些服务也设置为前置条件
  - 接口设计响应不直接返回 200, 301, 302, 4xx, 5xx 这种 http 状态码原生返回是基于什么原因

同时更新并校对其他文档是否有冲突的设计或者描述, 发现后请立即修改

根据现有的文档实现程序, 测试过程中需要调用到大模型生成结构化文档接口的, 需要使用 mock 逻辑进行实现

请详细分析列表中的问题并解决:

- 创建世界中的 ai 辅助生成失败:
  - [GIN] 2025/08/04 - 14:48:48 | 204 |      22.193µs |       127.0.0.1 | OPTIONS  "/api/v1/ai/generate/scene"
  [GIN] 2025/08/04 - 14:48:48 | 404 |      80.346µs |       127.0.0.1 | POST     "/api/v1/ai/generate/scene"
- 创建新世界没有保存到数据库, 没有看到调试日志请求生成该世界的结构化数据
- 进入游戏后不要显示其他与游戏界面无关的 dom 元素, 例如头部和尾部
- 出现报错, 日志如下:
  - [GIN] 2025/08/04 - 14:39:42 | 204 |       4.759µs |       127.0.0.1 | OPTIONS  "/api/v1/game/worlds/world-1/characters?page=1&limit=50&character_type=npc"
  [GIN] 2025/08/04 - 14:39:42 | 204 |       4.508µs |       127.0.0.1 | OPTIONS  "/api/v1/game/worlds/world-1/characters?page=1&limit=50&character_type=player"
  [GIN] 2025/08/04 - 14:39:42 | 404 |      27.103µs |       127.0.0.1 | GET      "/api/v1/game/worlds/world-1/characters?page=1&limit=50&character_type=player"
  [GIN] 2025/08/04 - 14:39:42 | 404 |       3.717µs |       127.0.0.1 | GET      "/api/v1/game/worlds/world-1/characters?page=1&limit=50&character_type=npc"

- 将 generateScene 生成场景（模拟AI生成）修改为使用 windmill 生成 json 结构化数据接口
- generateScene 生成场景调用真实 ai 接口后需要异步获取生成的数据, 前端页面需要设置 loading 状态进行等待
- 点击创建世界的按钮事件应该是创建当前配置的世界并通过使用 windmill 生成 json 结构化数据, 等待接口 loading 完成即进入新创建的世界, 而不是跳转固定的"魔法森林"世界
- 进入游戏后出现 http://localhost:8080/api/v1/game/worlds/world-new/characters?page=1&limit=10 地址 404
- 游戏界面需要有返回主菜单的功能

- 去除 getWorlds 模拟的世界列表, 使用真正的接口查询
- 进入游戏应该是进入选择的游戏的地址, 而不是 "world-new"
- 创建游戏世界返回 201 状态码, [GIN] 2025/08/04 - 18:03:18 | 201 |     100.385µs |       127.0.0.1 | POST     "/api/v1/game/worlds", 但是没有生成世界数据

- 创建世界时使用 ai 辅助生成报错: AI生成的内容格式异常，请手动填写描述
- 选择刚创建的世界, 点击进入提示不存在: [GIN] 2025/08/04 - 18:39:40 | 404 |      30.419µs |       127.0.0.1 | GET      "/api/v1/game/worlds/7c53bb82-1047-43ea-9929-20ea4b20e169"

使用 windmill 生成 json 结构化输出接口实现代码逻辑中的 ai 模拟数据, 如下操作:

- 根据场景所需要的 ai 结构化数据, 编写一个 JSON Schema接口定义, 具体参考下面的 typescript 示例
- 根据查询条件和场景创建符合项目需求的提示词和系统角色
- 调用 windmill 生成 json 结构化输出接口
- 处理 windmill 生成 json 结构化输出接口异步返回的结构, 按照每个场景逻辑中的要求更新数据库和前端显示

```typescript
/**
 * JSON Schema接口定义
 * @description 用于定义AI服务返回数据的结构化格式
 */
export interface JSONSchema {
    /** 数据类型 (object, array, string, number, boolean) */
    type: string;
    /** 数组项的schema定义 */
    items?: JSONSchema;
    /** 对象属性的schema定义 */
    properties?: { [key: string]: JSONSchema };
    /** 必需的属性列表 */
    required?: string[];
    /** 属性排序 */
    propertyOrdering?: string[];
    /** 枚举值列表 */
    enum?: string[];
    /** 数据格式 (date-time, email等) */
    format?: string;
    /** 字段描述 */
    description?: string;
    /** 数字类型的最小值 */
    minimum?: number;
    /** 数字类型的最大值 */
    maximum?: number;
}
```

@game_schemas.go 中定义的 json schema 不符合 @需求文档.md 和 @数据设计文档.md 中的描述, 专用JSON Schema 包括以下几个场景:

- 创建世界时, 需要根据接口传递的世界名称和世界设定 (可选) 生成多个详细的世界描述供玩家选择
  - 世界描述的内容包括但不限制于世界的环境, 文化, 历史背景, 地理位置等
  - 世界规则 (可选) 包括但不限制于游戏规则和限制等
- 进入新的世界时, 需要根据世界名称, 世界设定 (可选), 世界描述, 世界规则 (可选) 生成当前世界的某一个场景, 包括该场景的连接场景
  - 场景的 json schema 除了包括场景的基本信息, 还需要包括事件列表, 物品列表, 角色列表等内容
  - 需要提供游戏时间来确定场景的事件列表
- 角色探索世界时, 按需根据世界名称, 世界设定 (可选), 世界描述, 世界规则 (可选) 和周围的场景信息生成连接的场景列表
  - 场景的 json schema 除了包括场景的基本信息, 还需要包括事件列表, 物品列表, 角色列表等内容
  - 需要提供游戏时间来确定场景的事件列表
- 基于心跳生成世界进程, 根据世界名称, 世界设定 (可选), 世界描述, 世界规则 (可选), 当前的游戏时间, 将现有场景中的信息, 包括事件列表, 物品列表, 角色列表等内容提供给大模型生成当前时间的变化
  - 返回的 json schema 为一个列表, 每一项包括场景的 id 和场景内新的的事件列表, 物品列表, 角色列表等内容
- 书信对话生成, 角色间的书信对话, 提供角色的特性以及相关的记忆和书信历史, 返回的 json schema 为一个列表, 每一项包括回信的内容和对话的角色
- 事件触发结果生成, 角色和事件以及物品间的交互, 提供事件和物品的描述, 角色的特性以及相关的阅历, 对事件的行动, 返回的 json schema 为一个列表, 每一项包括事件的结果, 事件的id, 角色的id 和角色的行动

结合 @需求文档.md 和 @数据设计文档.md 中的内容, 以及 @六个核心场景JSON_Schema设计文档.md 中的 json schema 和 models 中定义的模型类分析:

- 数据库设计是否存在问题, 可以怎么解决
- 场景表中的 connections 需要记录哪些数据才能描述两个场景间的关系
- 角色表的设计能支持一个用户对应多个角色吗
- 实体表的通用设计会不会大幅提高程序的复杂度
- 游戏事件表中怎么保存事件的处理结果和耗时

分析上面的问题并进行处理

- 将优化过的数据库设计更新到 @数据设计文档.md, 并且补充上对应游戏场景中数据库的查询用例
- 完善 @数据设计文档.md 中核心模型的设计

分析并处理 @数据设计文档.md 中的问题, 结合 @需求文档.md

- 实体表已经拆分为物品表, 事件表, 目标表, 那还有需要通用的实体表吗
- 角色阅历详细表按照逻辑应该是从角色记忆详细表总结而来, 这些字段的设计合理吗
- 游戏事件日志表需要拆分出事件处理结果表和事件处理步骤详细记录表还有事件耗时统计表吗
- 世界规模框架表和玩家初始化分配表是怎么作用的
- 为核心的 jsonb 字段添加和场景表的 connections 字段的 json 结构规范说明
- 为数据库表设计添加更详细的中文说明
- 完整的描述数据库各个表的关联关系

将相关的优化和中文说明更新到 @数据设计文档.md 中

根据 @数据设计文档.md 中的数据库表的定义, 结合 @需求文档.md 中的内容, 对每个表进行详细分析:

- 详细说明每个表对应的应用场景列表
- 根据应用场景分析该表的字段设计是否合理
- 对于需要量化表示, 或者枚举表示的字段是否会影响到游戏世界创建的自由度
- 玩家创建的各种世界, 可能存在不同的世界观, 表字段中是否存在某些字段是需要特定世界观的?
- 有哪些字段是没有具体的使用场景, 或者是很少被使用的, 能否进行优化

请分析所有的数据库表并将结果生成为文档 "数据库表使用场景分析.md"

根据 @数据设计文档.md 和 @数据库表使用场景分析.md 中的内容, 以及 @需求文档.md 中的内容, 优化数据库表的设计:

- 设计过程中需要考虑到如果表数据是由 ai 生成的, 那如何设计才能更好的支持 ai 的生成, 比如尽量减少量化表示, 减少各种关联关系等等
- 去除专为某些特定世界设计的字段
- 优化表的关联关系, 避免过度冗余
- 合并相似的字段, 去除不需要的字段, 减少冗余
- 为每个字段条件中文说明, 复杂的 json 字段请补充上 json 对象的结构和每个字段的中文说明

请将优化后的数据库表设计保存到 "数据库表设计文档.md" 中

开发模式下或者部分简单部署场景使用 sqlite 数据库, 需要怎么设计数据库表才能更好的支持 pgsql 同时兼容 sqlite, 除了下列的两种方法, 还有哪些更优的方法:

- 数据库表设计时, 使用 pgsql 专有的优化方式设计 sql, 由程序逻辑实现支持 sqlite
- 数据库表设计时, 使用通用的 sql 语法, 通过程序逻辑实现各种业务流程

请结合 @数据库表设计文档.md 列举出更多的优化方案, 分析上述方法的优缺点和可行性, 请详细说明你认为的最优方案并设计实现方案, 将所有的分析和说明保存为 "数据库表兼容性设计文档.md"

结合当前项目的实际情况, 以及 @数据库表兼容性设计文档.md 中的内容, 优化数据库兼容方案, 并更新文档, 根据数据库表的设计完成兼容方案的实现


